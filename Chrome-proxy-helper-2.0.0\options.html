<html>
<head>
    <title>Options</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <link rel="stylesheet" type="text/css" href="stylesheets/chrome-bootstrap.css" />
    <link rel="stylesheet" type="text/css" href="stylesheets/options.css" />
    <script type="text/javascript" src="javascripts/jquery-3.6.0.min.js"></script>
    <script type="text/javascript" src="javascripts/options-ui.js"></script>
    <script type="text/javascript" src="javascripts/options.js"></script>
</head>
<body class="chrome-bootstrap">
    <div class="frame">
        <div class="navigation">
            <h1>
                <a style="font-size:18px" data-i18n-content="Options">Options</a>
            </h1>

            <ul class="menu">
                <li class="selected">
                    <a href="#general" data-i18n-content="General">General</a>
                </li>
                <li>
                    <a href="#pac-config" data-i18n-content="PAC">PAC</a>
                </li>
                <li>
                    <a href="#rules" data-i18n-content="Rules">Rules</a>
                </li>
                <li>
                    <a href="#auth-config" data-i18n-content="authConfig">Authentication</a>
                </li>
            </ul>
            <ul class="menu">
                <li>
                    <a href="#about" data-i18n-content="About">About</a>
                </li>
            </ul>
        </div>
        <div class="mainview view">
        <div id="general" class="selected">
            <header><h1></h1></header>
            <div class="content">
                <h3 data-i18n-content="proxy_servers">Proxy servers</h3>
                <section>
                    <div>
                        <span class="ip_label" data-i18n-content="http_proxy">HTTP PROXY:</span>
                        <input type="text" id="http-host" size="15"/>
                        <span class="port_label" data-i18n-content="port">PORT:</span>
                        <input type="text" size="6" id="http-port"/>
                    </div>
                    <div>
                        <span class="ip_label" data-i18n-content="https_proxy">HTTPS PROXY:</span>
                        <input type="text" id="https-host" size="15"/>
                        <span class="port_label" data-i18n-content="port">PORT:</span>
                        <input type="text" size="6" id="https-port"/>
                    </div>
                    <div>
                        <span class="ip_label" data-i18n-content="socks_proxy">SOCKS PROXY:</span>
                        <input type="text" id="socks-host" size="15"/>
                        <span class="port_label" data-i18n-content="port">PORT:</span>
                        <input type="text" size="6" id="socks-port"/>
                    </div>
                    <div class="socks">
                        <input type="radio" id="socks4" name="socks"/>
                        <span style="font-size:80%;">SOCKS4</span>
                        <input type="radio" id="socks5" name="socks"/>
                        <span style="font-size:80%;">SOCKS5</span>
                    </div>
                    <div>
                        <span class="ip_label" data-i18n-content="quic_proxy">QUIC PROXY:</span>
                        <input type="text" id="quic-host" size="15"/>
                        <span class="port_label" data-i18n-content="port">PORT:</span>
                        <input type="text" size="6" id="quic-port"/>
                    </div>
                </section>
                <h3 data-i18n-content="advanced_settings">Advanced settings</h3>
                <section>
                    <div>
                        <span class="field_label" style="width: 120px;" data-i18n-content="proxy_rules">Proxy mode: </span>
                        <select id="proxy-rule" >
                            <option value="singleProxy">singleProxy</option>
                            <option value="proxyForHttp">proxyForHttp</option>
                            <option value="proxyForHttps">proxyForHttps</option>
                            <option value="proxyForFtp">proxyForFtp</option>
                            <option value="fallbackProxy">fallbackProxy</option>
                        </select>
                    </div>
                </section>
            </div>
        </div>
        <div id="rules">
            <header><h1></h1></header>
            <div class="content">
                <!--
                <section>
                    <h3 data-i18n-content="rules_mode">Rules Mode</h3>
                    <select id='rules-mode'>
                        <option data-i18n-content="Whitelist" value="Whitelist">Whitelist</option>
                        <option data-i18n-content="Blacklist" value="Blacklist">Blacklist</option>
                    </select>
                </section>
                -->
                <section id='whitelist'>
                    <h3 data-i18n-content="config_bypasslist">Bypass List</h3>
                    <p data-i18n-content="domain_in_bypasslist">Domains in bypass list will Never use a proxy</p>
                    <div class="section">
                        <textarea id="bypasslist" cols="55" rows="10" placeholder="***********/16,*.foobar.com,*foobar.com:99,https://x.*.y.com:99"></textarea>
                        <a href='#' class='tooltip'>
                            <img src="images/help.gif">
                            <span>
                                <img class="callout" src="images/callout.gif"/>
                                ***********/16,  <br/>
                                *.foobar.com,    <br/>
                                *foobar.com:99,  <br/>
                                https://x.*.y.com:99
                            </span>
                        </a>
                    </div>
                    <h4 data-i18n-content="use_predefine_list">Use internal proxy bypass list</h3>
                    <div class="checkbox">
                        <input id='china-list' type="checkbox" id="use-china-list"/>
                        <span data-i18n-content="china_list">China List</span>
                    </div>
                </section>
                <!--
                <section id='blacklist' style="display: none;">
                    <h3 data-i18n-content="config_proxylist">Proxy List</h3>
                    <p data-i18n-content="domain_in_proxy">Domains in list will use a proxy</p>
                    <div class="section">
                        <textarea id="proxylist" cols="40" rows="10" placeholder=""></textarea>
                    </div>
                </section>
                -->
            </div>
        </div>
        <div id="auth-config">
           <header><h1></h1></header>
           <div class="content">
           <h3 data-i18n-content="authentication">Authentication</h3>
           <section>
                <div>
                    <span class="field_label" style="width: 100px;" data-i18n-content="user">Username:</span>
                    <input type="text" id="username" size="15"></input>
                <a href='#' class='tooltip'>
                    <img src="images/help.gif">
                    <span>
                        <img class="callout" src="images/callout.gif"/>
                        <strong>chrome api doesn't support socks authentication</strong><br/>
                    </span>
                </a>
                </div>
                <div>
                    <span class="field_label" style="width: 100px;" data-i18n-content="pass">Password:</span>
                    <input type="password" id="password" size="15"></input>
                </div>
            </section>
            </div>
        </div>
        <div id="pac-config">
            <header><h1></h1></header>
            <div class="content">
            <h3>Pac URL</h3>
            <section>
                <!-- <span class="field_label" style="width: 100px;" data-i18n-content="pac_path">PAC PATH:</span> -->
                <div>
                <select id="pac-type">
                    <option>http://</option>
                    <option>https://</option>
                </select>
                <input type="text" size="30" id="pac-script-url" placeholder="127.0.0.1/proxy.pac"></input>
                <a href='#' class='tooltip'>
                    <img src="images/help.gif">
                    <span>
                        <img class="callout" src="images/callout.gif"/>
                        <strong>Remote pac script:</strong><br/>
                        http://127.0.0.1/proxy.pac
                        <br>
                        https://127.0.0.1/proxy.pac
                    </span>
                </a>
                <div>
            </section>
            <h3 data-i18n-content="pac_data">Pac Script</h3>
            <section>
                <div>
                <input type="file" id="pac-file" />
                </div>
                <div>
                <textarea id="pac-data" cols="55" rows="15" style="margin-top:.5em; overflow-x:hidden"></textarea>
                </div>
            </section>
            </div>
        </div>
        <div id="about">
            <header><h1 data-i18n-content="About">About</h1></header>
            <div class="content">
            <h3>Chrome Web Store</h3>
            <section class="section">
                <img src="images/chrome_web_store.png"></img>
                <a href="https://chrome.google.com/webstore/detail/proxy-helper/mnloefcpaepkpmhaoipjkpikbnkmbnic">Proxy Helper</a>
            </section>
            <h3>Open Source Licenses</h3>
            <section>
                <div>
                    <table class="simpletable">
                        <thead>
                            <tr>
                                <th>Library</th>
                                <th>License</th>
                            </tr>
                        </thead>
                        <tbody style="display: table-row-group;">
                            <tr>
                                <td><a href="https://jquery.org" target="_blank">jQuery</a></td>
                                <td><a href="https://github.com/jquery/jquery/blob/master/LICENSE.txt" target="_blank">LICENSE.txt</a></td>
                            </tr>
                            <tr>
                                <td><a href="https://github.com/roykolak/chrome-bootstrap" target="_blank">chrome-bootstrap</a></td>
                                <td><a href="http://opensource.org/licenses/mit-license.php" target="_blank">MIT</a></td>
                            </tr>
                            <tr></tr>
                            <tr></tr>
                        </tbody>
                    </table>
                </div>
            </section>
            </div>
        </div>

    </div>
    </div>
</body>
</html>
