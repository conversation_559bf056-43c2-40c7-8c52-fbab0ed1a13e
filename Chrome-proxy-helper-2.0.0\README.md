# Introduction
By default, Chrome use the system proxy setting (IE proxy settings on Windows platform ),
but sometime we want to set proxy *ONLY* for chrome, not the whole system. Chrome proxy 
helper extension use Chrome native proxy API to set proxy, support  socks5, socks4, http 
and https protocol and pac script, Fast And Simple.

# Features
* support socks4, socks5, http, https, and quic proxy settings
* support pac proxy settings
* support bypass list
* support online/offline pac script
* support customer proxy rules
* support proxy authentication
* support extension settings synchronize

# Install
* Install the latest stable version on chrome web store by click [here](https://chrome.google.com/webstore/detail/proxy-helper/mnloefcpaepkpmhaoipjkpikbnkmbnic).
* Install the unstable version by cloning the [Git](https://github.com/henices/Chrome-proxy-helper.git) repository:

```
    git clone https://github.com/henices/Chrome-proxy-helper.git
```

# Document

* [Help](https://github.com/henices/Chrome-proxy-helper/wiki)
* [FAQ](https://github.com/henices/Chrome-proxy-helper/wiki/FAQ)

# LICENSE
This program is free software: you can redistribute it and/or modify
it under the terms of the GNU General Public License as published by
the Free Software Foundation, either version 2 of the License, or
(at your option) any later version.

This program is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
GNU General Public License for more details.
 
You should have received a copy of the GNU General Public License
along with this program.  If not, see <http://www.gnu.org/licenses/>

