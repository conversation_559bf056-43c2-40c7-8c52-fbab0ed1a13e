// HTTP代理自动配置
let currentConfig = null;
let localStorage = {};

// 扩展启动时初始化
chrome.runtime.onInstalled.addListener(async () => {
  console.log('HTTP Proxy Auto Config 扩展已安装');
  await initializeStorage();
  loadAndApplyConfig();
});

chrome.runtime.onStartup.addListener(async () => {
  await initializeStorage();
  loadAndApplyConfig();
});

// 定期检查配置文件变化
setInterval(loadAndApplyConfig, 5000);

// 初始化存储
async function initializeStorage() {
  const result = await chrome.storage.local.get(null);
  Object.assign(localStorage, result);
  console.log('存储已初始化:', localStorage);
}

// 代理认证处理器
async function handleProxyAuth(details) {
  console.log('代理认证请求:', details);

  // 确保存储已初始化
  if (!localStorage.proxyConfig) {
    await initializeStorage();
  }

  if (localStorage.proxyConfig) {
    const config = JSON.parse(localStorage.proxyConfig);
    if (config.username && config.password) {
      console.log('提供代理认证信息');
      return {
        authCredentials: {
          username: config.username,
          password: config.password
        }
      };
    }
  }

  console.log('无认证信息可用');
  return {};
}

// 注册代理认证监听器
chrome.webRequest.onAuthRequired.addListener(
  handleProxyAuth,
  { urls: ["<all_urls>"] },
  ['asyncBlocking']
);

async function loadAndApplyConfig() {
  try {
    console.log('正在加载配置文件...');
    // 从本地文件系统读取配置（通过fetch）
    const response = await fetch(chrome.runtime.getURL('proxy-config.json'));
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    const config = await response.json();
    console.log('配置文件内容:', config);

    // 检查配置是否有变化
    if (JSON.stringify(config) !== JSON.stringify(currentConfig)) {
      console.log('配置已变化，正在应用新配置...');
      currentConfig = config;

      // 保存配置到存储中供认证使用
      localStorage.proxyConfig = JSON.stringify(config);
      await chrome.storage.local.set(localStorage);

      if (config.enabled && config.host && config.port) {
        await setProxy(config);
        console.log('代理已启用:', `${config.host}:${config.port}`);
      } else {
        await clearProxy();
        console.log('代理已禁用或配置不完整');
      }
    } else {
      console.log('配置无变化');
    }
  } catch (error) {
    console.error('加载配置失败:', error);
  }
}

async function setProxy(config) {
  console.log('开始设置代理:', config);

  // 设置代理配置
  const proxyConfig = {
    mode: "fixed_servers",
    rules: {
      singleProxy: {
        scheme: "http",
        host: config.host,
        port: parseInt(config.port)
      }
    }
  };

  try {
    await chrome.proxy.settings.set({
      value: proxyConfig,
      scope: 'regular'
    });
    console.log('代理设置成功:', `${config.host}:${config.port}`);

    if (config.username && config.password) {
      console.log('代理认证信息已准备就绪');
    }

    // 验证设置是否生效
    const result = await chrome.proxy.settings.get({});
    console.log('当前代理设置:', result);
  } catch (error) {
    console.error('设置代理失败:', error);
  }
}

async function clearProxy() {
  try {
    console.log('正在清除代理设置...');
    await chrome.proxy.settings.clear({
      scope: 'regular'
    });
    console.log('代理设置已清除');

    // 验证清除是否生效
    const result = await chrome.proxy.settings.get({});
    console.log('清除后的代理设置:', result);
  } catch (error) {
    console.error('清除代理失败:', error);
  }
}
