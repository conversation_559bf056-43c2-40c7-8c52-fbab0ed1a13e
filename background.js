// HTTP代理自动配置
let currentConfig = null;

// 扩展启动时初始化
chrome.runtime.onInstalled.addListener(() => {
  console.log('HTTP Proxy Auto Config 扩展已安装');
  loadAndApplyConfig();
});

chrome.runtime.onStartup.addListener(() => {
  loadAndApplyConfig();
});

// 定期检查配置文件变化
setInterval(loadAndApplyConfig, 5000);

async function loadAndApplyConfig() {
  try {
    // 从本地文件系统读取配置（通过fetch）
    const response = await fetch(chrome.runtime.getURL('proxy-config.json'));
    const config = await response.json();
    
    // 检查配置是否有变化
    if (JSON.stringify(config) !== JSON.stringify(currentConfig)) {
      currentConfig = config;
      
      if (config.enabled && config.host && config.port) {
        await setProxy(config);
        console.log('代理已启用:', `${config.host}:${config.port}`);
      } else {
        await clearProxy();
        console.log('代理已禁用');
      }
    }
  } catch (error) {
    console.error('加载配置失败:', error);
  }
}

async function setProxy(config) {
  let proxyConfig;

  if (config.username && config.password) {
    // 使用PAC脚本处理认证
    const pacScript = `
      function FindProxyForURL(url, host) {
        return "PROXY ${config.username}:${config.password}@${config.host}:${config.port}";
      }
    `;

    proxyConfig = {
      mode: "pac_script",
      pacScript: {
        data: pacScript
      }
    };
  } else {
    // 无认证的简单代理
    proxyConfig = {
      mode: "fixed_servers",
      rules: {
        singleProxy: {
          scheme: "http",
          host: config.host,
          port: parseInt(config.port)
        }
      }
    };
  }

  await chrome.proxy.settings.set({
    value: proxyConfig,
    scope: 'regular'
  });

  console.log('代理配置已设置:', `${config.host}:${config.port}`, config.username ? '(带认证)' : '(无认证)');
}

async function clearProxy() {
  await chrome.proxy.settings.clear({
    scope: 'regular'
  });
}
