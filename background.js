// HTTP代理自动配置
let currentConfig = null;
let localStorage = {};

// 扩展启动时初始化
chrome.runtime.onInstalled.addListener(async () => {
  console.log('HTTP Proxy Auto Config 扩展已安装');
  await initializeStorage();
  loadAndApplyConfig();
});

chrome.runtime.onStartup.addListener(async () => {
  await initializeStorage();
  loadAndApplyConfig();
});

// 定期检查配置文件变化
setInterval(loadAndApplyConfig, 5000);

// 初始化存储
async function initializeStorage() {
  const result = await chrome.storage.local.get(null);
  Object.assign(localStorage, result);
  console.log('存储已初始化:', localStorage);
}

// 代理认证处理器
function handleProxyAuth(details) {
  console.log('代理认证请求:', details);

  if (localStorage.proxyConfig) {
    const config = JSON.parse(localStorage.proxyConfig);
    if (config.username && config.password) {
      console.log('提供代理认证信息 - 用户名:', config.username);
      return {
        authCredentials: {
          username: config.username,
          password: config.password
        }
      };
    }
  }

  console.log('无认证信息可用');
  return {};
}

// 注册代理认证监听器
chrome.webRequest.onAuthRequired.addListener(
  handleProxyAuth,
  { urls: ["<all_urls>"] },
  ['blocking']
);

// 添加代理错误监听器
chrome.proxy.onProxyError.addListener(function(details) {
  console.error('代理错误:', details);
  console.error('错误详情:', {
    fatal: details.fatal,
    error: details.error,
    details: details.details
  });
});

// 处理来自调试页面的消息
chrome.runtime.onMessage.addListener((message, _sender, sendResponse) => {
  if (message.action === 'reloadConfig') {
    console.log('收到重新加载配置请求');
    loadAndApplyConfig().then(() => {
      sendResponse({success: true});
    }).catch(error => {
      console.error('重新加载配置失败:', error);
      sendResponse({success: false, error: error.message});
    });
    return true; // 保持消息通道开放
  }
});

async function loadAndApplyConfig() {
  try {
    console.log('正在加载配置文件...');
    // 从本地文件系统读取配置（通过fetch）
    const response = await fetch(chrome.runtime.getURL('proxy-config.json'));
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    const config = await response.json();
    console.log('配置文件内容:', config);

    // 检查配置是否有变化
    if (JSON.stringify(config) !== JSON.stringify(currentConfig)) {
      console.log('配置已变化，正在应用新配置...');
      currentConfig = config;

      // 保存配置到存储中供认证使用
      localStorage.proxyConfig = JSON.stringify(config);
      await chrome.storage.local.set(localStorage);

      if (config.enabled && config.host && config.port) {
        await setProxy(config);
        console.log('代理已启用:', `${config.host}:${config.port}`);
      } else {
        await clearProxy();
        console.log('代理已禁用或配置不完整');
      }
    } else {
      console.log('配置无变化');
    }
  } catch (error) {
    console.error('加载配置失败:', error);
  }
}

async function setProxy(config) {
  console.log('开始设置代理:', config);

  // 设置代理配置
  const proxyConfig = {
    mode: "fixed_servers",
    rules: {
      singleProxy: {
        scheme: "http",
        host: config.host,
        port: parseInt(config.port)
      }
    }
  };

  try {
    console.log('正在应用代理配置:', proxyConfig);
    await chrome.proxy.settings.set({
      value: proxyConfig,
      scope: 'regular'
    });
    console.log('✅ 代理设置成功:', `${config.host}:${config.port}`);

    if (config.username && config.password) {
      console.log('✅ 代理认证信息已准备就绪 - 用户名:', config.username);
    }

    // 验证设置是否生效
    const result = await chrome.proxy.settings.get({});
    console.log('✅ 当前代理设置验证:', JSON.stringify(result.value, null, 2));

    // 测试代理连接
    console.log('🔍 开始测试代理连接...');
    testProxyConnection();
  } catch (error) {
    console.error('❌ 设置代理失败:', error);
  }
}

// 测试代理连接
async function testProxyConnection() {
  try {
    console.log('正在测试代理连接...');
    const response = await fetch('https://httpbin.org/ip', {
      method: 'GET',
      cache: 'no-cache'
    });

    if (response.ok) {
      const data = await response.json();
      console.log('✅ 代理连接测试成功，当前IP:', data.origin);
    } else {
      console.error('❌ 代理连接测试失败，HTTP状态:', response.status);
    }
  } catch (error) {
    console.error('❌ 代理连接测试异常:', error.message);
  }
}

async function clearProxy() {
  try {
    console.log('正在清除代理设置...');
    await chrome.proxy.settings.clear({
      scope: 'regular'
    });
    console.log('代理设置已清除');

    // 验证清除是否生效
    const result = await chrome.proxy.settings.get({});
    console.log('清除后的代理设置:', result);
  } catch (error) {
    console.error('清除代理失败:', error);
  }
}
