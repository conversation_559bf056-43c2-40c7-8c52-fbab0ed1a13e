// HTTP代理自动配置
let currentConfig = null;

// 扩展启动时初始化
chrome.runtime.onInstalled.addListener(() => {
  console.log('HTTP Proxy Auto Config 扩展已安装');
  loadAndApplyConfig();
});

chrome.runtime.onStartup.addListener(() => {
  loadAndApplyConfig();
});

// 定期检查配置文件变化
setInterval(loadAndApplyConfig, 5000);

async function loadAndApplyConfig() {
  try {
    // 从本地文件系统读取配置（通过fetch）
    const response = await fetch(chrome.runtime.getURL('proxy-config.json'));
    const config = await response.json();
    
    // 检查配置是否有变化
    if (JSON.stringify(config) !== JSON.stringify(currentConfig)) {
      currentConfig = config;
      
      if (config.enabled && config.host && config.port) {
        await setProxy(config);
        console.log('代理已启用:', `${config.host}:${config.port}`);
      } else {
        await clearProxy();
        console.log('代理已禁用');
      }
    }
  } catch (error) {
    console.error('加载配置失败:', error);
  }
}

async function setProxy(config) {
  // 设置代理配置
  const proxyConfig = {
    mode: "fixed_servers",
    rules: {
      singleProxy: {
        scheme: "http",
        host: config.host,
        port: parseInt(config.port)
      }
    }
  };

  await chrome.proxy.settings.set({
    value: proxyConfig,
    scope: 'regular'
  });

  // 如果有用户名和密码，设置认证
  if (config.username && config.password) {
    setupProxyAuth(config.username, config.password);
  }
}

async function clearProxy() {
  await chrome.proxy.settings.clear({
    scope: 'regular'
  });
  
  // 清除认证监听器
  if (chrome.webRequest.onAuthRequired.hasListener(handleAuth)) {
    chrome.webRequest.onAuthRequired.removeListener(handleAuth);
  }
}

function setupProxyAuth(username, password) {
  // 移除之前的监听器
  if (chrome.webRequest.onAuthRequired.hasListener(handleAuth)) {
    chrome.webRequest.onAuthRequired.removeListener(handleAuth);
  }

  // 创建认证处理函数
  function handleAuth(details) {
    return {
      authCredentials: {
        username: username,
        password: password
      }
    };
  }

  // 添加认证监听器
  chrome.webRequest.onAuthRequired.addListener(
    handleAuth,
    { urls: ["<all_urls>"] },
    ["blocking"]
  );
}
