body { width: 140px; font-family: Helvetica, 'Source Han Sans CN', 'PingFang SC', 'Chrome Droid Sans', 'Droid Sans Fallback', 'Lucida Grande', 'Microsoft YaHei', <PERSON>l, sans-serif; font-size: 13px; color: #333; font-weight: 500; padding: 2px; margin: 0; overflow: hidden; }
.menu { line-height: 26px; white-space: nowrap; background-repeat: no-repeat no-repeat; background-position: 6px 5px; background: transparent; display: block; }
.menu .text {margin-left: .5em;}
ul { list-style: none; text-align:center; margin: 0px; padding: 0px; }
li { cursor: pointer; margin: 0px; padding: 0px; clear: both; overflow: hidden; text-align: center; border: 1px solid white; }
ul li:hover {border-radius: 3px 3px;border: solid 1px #c5cdd3;}
.separator { display: none; width: 100%; border-top: 1px solid #ddd; }
.selected { color: #15C; font-weight: bolder; background-image: url(/images/checked.gif); background-repeat: no-repeat; background-position: 0.2em; }

