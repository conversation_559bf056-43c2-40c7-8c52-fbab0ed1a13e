# HTTP Proxy Auto Config - Chrome扩展

简单的Chrome浏览器扩展，用于自动设置HTTP密码代理。参考Chrome-proxy-helper的实现方式。

## 功能

- 自动读取配置文件并设置HTTP代理
- 支持用户名密码认证（使用webRequestAuthProvider）
- 每5秒自动检查配置变化
- 基于Chrome Manifest V3

## 安装

1. 打开Chrome浏览器，进入 `chrome://extensions/`
2. 开启"开发者模式"
3. 点击"加载已解压的扩展程序"
4. 选择此文件夹

## 配置

编辑 `proxy-config.json` 文件：

```json
{
  "enabled": true,
  "host": "proxy.example.com",
  "port": 8080,
  "username": "your_username",
  "password": "your_password"
}
```

## 参数说明

| 参数 | 类型 | 说明 | 示例 |
|------|------|------|------|
| `enabled` | boolean | 是否启用代理 | `true` |
| `host` | string | 代理服务器地址 | `"127.0.0.1"` |
| `port` | number | 代理服务器端口 | `8080` |
| `username` | string | 用户名（可选） | `"myuser"` |
| `password` | string | 密码（可选） | `"mypass"` |

## 使用示例

### 启用代理
```json
{
  "enabled": true,
  "host": "*************",
  "port": 8080,
  "username": "user",
  "password": "pass"
}
```

### 禁用代理
```json
{
  "enabled": false,
  "host": "*************",
  "port": 8080,
  "username": "user",
  "password": "pass"
}
```

保存配置文件后，扩展会在5秒内自动应用新设置。

## 调试

1. **查看日志**：在 `chrome://extensions/` 中找到扩展，点击"检查视图" → "背景页"查看控制台日志
2. **测试代理**：访问 http://httpbin.org/ip 查看当前IP是否为代理IP
3. **认证测试**：如果代理需要认证，扩展会自动处理，无需手动输入

## 技术实现

- 使用 `webRequestAuthProvider` 权限处理代理认证
- 使用 `asyncBlocking` 模式拦截认证请求
- 参考 Chrome-proxy-helper 2.0.0 的实现方式
- 支持 Chrome Manifest V3
