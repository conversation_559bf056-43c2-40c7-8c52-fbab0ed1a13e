# HTTP Proxy Auto Config - Chrome扩展

这是一个Chrome浏览器扩展，用于自动设置HTTP代理，支持用户名密码认证。

## 功能特性

- 🚀 自动读取配置文件并应用代理设置
- 🔐 支持HTTP代理用户名密码认证
- 📝 通过JSON配置文件进行配置，无需图形界面
- 🔄 自动检测配置文件变化并实时应用
- 🛡️ 支持多种代理协议（HTTP、HTTPS、SOCKS4、SOCKS5）

## 安装方法

1. 下载或克隆此项目到本地
2. 打开Chrome浏览器，进入扩展管理页面 (`chrome://extensions/`)
3. 开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择项目文件夹

## 配置方法

### 方法1：直接编辑配置文件

编辑项目目录中的 `proxy-config.json` 文件：

```json
{
  "enabled": true,
  "scheme": "http",
  "host": "proxy.example.com",
  "port": 8080,
  "username": "your_username",
  "password": "your_password"
}
```

### 方法2：使用开发者工具

1. 在任意网页按F12打开开发者工具
2. 在Console中输入以下命令：

```javascript
// 启用代理
configManager.enableProxy('proxy.example.com', 8080, 'username', 'password');

// 禁用代理
configManager.disableProxy();

// 查看当前配置
configManager.getConfig().then(config => console.log(config));
```

## 配置参数说明

| 参数 | 类型 | 说明 | 示例 |
|------|------|------|------|
| `enabled` | boolean | 是否启用代理 | `true` |
| `scheme` | string | 代理协议 | `"http"`, `"https"`, `"socks4"`, `"socks5"` |
| `host` | string | 代理服务器地址 | `"127.0.0.1"` |
| `port` | number | 代理服务器端口 | `8080` |
| `username` | string | 用户名（可选） | `"myuser"` |
| `password` | string | 密码（可选） | `"mypass"` |

## 配置示例

### HTTP代理（带认证）
```json
{
  "enabled": true,
  "scheme": "http",
  "host": "proxy.company.com",
  "port": 8080,
  "username": "employee",
  "password": "secret123"
}
```

### SOCKS5代理（无认证）
```json
{
  "enabled": true,
  "scheme": "socks5",
  "host": "127.0.0.1",
  "port": 1080,
  "username": "",
  "password": ""
}
```

### 禁用代理
```json
{
  "enabled": false,
  "scheme": "http",
  "host": "127.0.0.1",
  "port": 8080,
  "username": "",
  "password": ""
}
```

## 使用说明

1. **配置更新**：修改配置文件后，扩展会在5秒内自动检测并应用新配置
2. **认证处理**：如果配置了用户名和密码，扩展会自动处理代理认证
3. **日志查看**：可以在扩展的后台页面查看日志信息

## 故障排除

1. **代理不生效**：
   - 检查配置文件格式是否正确
   - 确认 `enabled` 设置为 `true`
   - 验证代理服务器地址和端口

2. **认证失败**：
   - 检查用户名和密码是否正确
   - 确认代理服务器支持认证

3. **扩展无法加载**：
   - 检查manifest.json文件是否正确
   - 确认所有必需文件都存在

## 开发者工具

扩展提供了一个配置管理器，可以在浏览器控制台中使用：

```javascript
// 获取配置管理器
const cm = window.configManager;

// 查看当前配置
cm.getConfig().then(console.log);

// 启用代理
cm.enableProxy('127.0.0.1', 8080, 'user', 'pass');

// 禁用代理
cm.disableProxy();

// 导出配置到文件
cm.exportConfig();
```

## 注意事项

- 此扩展需要"proxy"权限来修改浏览器代理设置
- 配置文件中的密码以明文存储，请注意安全
- 扩展会每5秒检查一次配置变化
- 支持Chrome Manifest V3

## 许可证

MIT License
