# HTTP Proxy Auto Config - Chrome扩展

简单的Chrome浏览器扩展，用于自动设置HTTP密码代理。

## 功能

- 自动读取配置文件并设置HTTP代理
- 支持用户名密码认证
- 每5秒自动检查配置变化

## 安装

1. 打开Chrome浏览器，进入 `chrome://extensions/`
2. 开启"开发者模式"
3. 点击"加载已解压的扩展程序"
4. 选择此文件夹

## 配置

编辑 `proxy-config.json` 文件：

```json
{
  "enabled": true,
  "host": "proxy.example.com",
  "port": 8080,
  "username": "your_username",
  "password": "your_password"
}
```

## 参数说明

| 参数 | 类型 | 说明 | 示例 |
|------|------|------|------|
| `enabled` | boolean | 是否启用代理 | `true` |
| `host` | string | 代理服务器地址 | `"127.0.0.1"` |
| `port` | number | 代理服务器端口 | `8080` |
| `username` | string | 用户名（可选） | `"myuser"` |
| `password` | string | 密码（可选） | `"mypass"` |

## 使用示例

### 启用代理
```json
{
  "enabled": true,
  "host": "*************",
  "port": 8080,
  "username": "user",
  "password": "pass"
}
```

### 禁用代理
```json
{
  "enabled": false,
  "host": "*************",
  "port": 8080,
  "username": "user",
  "password": "pass"
}
```

保存配置文件后，扩展会在5秒内自动应用新设置。
