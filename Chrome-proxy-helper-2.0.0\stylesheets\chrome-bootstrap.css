.chrome-bootstrap {
  font-family: 'Noto Sans UI', 'Segoe UI', 'Chrome Droid Sans', 'Droid Sans Fallback', 'Lucida Grande', 'Tahoma', sans-serif;
  font-size: 12px;
  color: #303942;
  cursor: default;
  margin: 0;
  /* Headings
  ============================================== */

  /* Layout
  ============================================== */

  /* Header
  ============================================== */

  /* View sections
  ============================================== */

  /* Control bar
  ============================================== */

  /* Pagination
  ============================================== */

  /* Alert
  ============================================== */

  /* Tags
  ============================================== */

  /* Main menu
  ============================================== */

  /* Icons
  ============================================== */

  /* Highlightable list
  ============================================== */

  /* Input styling
  ============================================== */

  /* Focused --------------------------------- */

  /* Disabled --------------------------------- */

  /* Hovering --------------------------------- */

  /* Active --------------------------------- */

  /* Modal
  ============================================== */

}
.chrome-bootstrap a {
  border: none;
  color: #15C;
  cursor: pointer;
  text-decoration: underline;
  font-weight: normal;
}
.chrome-bootstrap a:hover,
.chrome-bootstrap a:focus {
  outline: none;
}
.chrome-bootstrap ul,
.chrome-bootstrap ol {
  padding: 0;
}
.chrome-bootstrap li {
  list-style-type: none;
}
.chrome-bootstrap dl,
.chrome-bootstrap dt,
.chrome-bootstrap dd {
  margin: 0;
}
.chrome-bootstrap button {
  cursor: pointer;
}
.chrome-bootstrap h1,
.chrome-bootstrap h2,
.chrome-bootstrap h3,
.chrome-bootstrap h4 {
  -webkit-user-select: none;
  font-weight: normal;
  line-height: 1;
}
.chrome-bootstrap h1 small,
.chrome-bootstrap h2 small,
.chrome-bootstrap h3 small,
.chrome-bootstrap h4 small {
  font-size: 15px;
  margin: 0 10px;
  color: #53637D;
}
.chrome-bootstrap h1 {
  -webkit-margin-after: 1em;
  -webkit-margin-before: 21px;
  -webkit-margin-start: 23px;
  height: 18px;
  font-size: 18px;
}
.chrome-bootstrap h1 a {
  color: #5C6166;
  text-decoration: none;
}
.chrome-bootstrap h3 {
  color: black;
  font-size: 1.2em;
  margin-bottom: 0.8em;
}
.chrome-bootstrap h4 {
  font-size: 1em;
  margin-bottom: 5px;
}
.chrome-bootstrap .frame .navigation {
  height: 100%;
  -webkit-margin-start: 0;
  position: fixed;
  -webkit-margin-end: 15px;
  width: 155px;
  z-index: 3;
}
.chrome-bootstrap .frame .view,
.chrome-bootstrap .frame .content {
  width: 738px;
  overflow-x: hidden;
}
.chrome-bootstrap .frame .content {
  padding-top: 55px;
}
.chrome-bootstrap .frame .content p {
  text-align: justify;
}
.chrome-bootstrap .frame .with_controls .content {
  padding-top: 104px;
}
.chrome-bootstrap .frame .view {
  -webkit-margin-start: 155px;
}
.chrome-bootstrap .frame .view a {
  font: inherit;
}
.chrome-bootstrap .frame .mainview > * {
  -webkit-margin-start: -20px;
  -webkit-transition: margin 100ms, opacity 100ms;
  opacity: 0;
  z-index: 0;
  position: absolute;
  top: 0;
  display: block;
}
.chrome-bootstrap .frame .mainview > .selected {
  -webkit-margin-start: 0;
  -webkit-transition: margin 200ms, opacity 200ms;
  -webkit-transition-delay: 100ms;
  z-index: 1;
  opacity: 1;
}
.chrome-bootstrap header {
  position: fixed;
  background-image: -webkit-linear-gradient(#ffffff, #ffffff 40%, rgba(255, 255, 255, 0.92));
  width: 738px;
  z-index: 2;
}
.chrome-bootstrap header h1 {
  padding: 21px 0 13px;
  margin: 0;
  border-bottom: 1px solid #EEE;
}
.chrome-bootstrap header .corner {
  position: absolute;
  right: 0px;
  top: 21px;
}
.chrome-bootstrap header .corner input[type="text"] {
  width: 210px;
}
.chrome-bootstrap header .corner.cancelable .delete {
  opacity: 1;
  top: 4px;
  right: 5px;
}
.chrome-bootstrap section {
  -webkit-padding-start: 18px;
  margin-bottom: 24px;
  margin-top: 8px;
  max-width: 800px;
}
.chrome-bootstrap section h3 {
  -webkit-margin-start: -18px;
}
.chrome-bootstrap section .row {
  display: block;
  margin: 0.65em 0;
}
.chrome-bootstrap .controls {
  -webkit-padding-end: 3px;
  -webkit-padding-start: 4px;
  -webkit-transition: padding 100ms, height 100ms, opacity 100ms;
  border-bottom: 1px solid #EEE;
  display: -webkit-box;
  overflow: hidden;
  padding: 13px 0;
  position: relative;
}
.chrome-bootstrap .controls .text {
  display: inline-block;
  margin-top: 4px;
}
.chrome-bootstrap .controls .spacer {
  -webkit-box-flex: 1;
}
.chrome-bootstrap ol.pagination li {
  margin: 0 2px;
  display: inline-block;
  line-height: 25px;
}
.chrome-bootstrap ol.pagination a {
  width: 25px;
  height: 24px;
  text-align: center;
  display: block;
  background: #F0F6FE;
  text-decoration: none;
}
.chrome-bootstrap ol.pagination a:hover,
.chrome-bootstrap ol.pagination a.selected {
  background: #8AAAED;
  color: #FFF;
}
.chrome-bootstrap .alert {
  border-radius: 3px;
  background: rgba(147, 184, 252, 0.2);
  display: block;
  position: relative;
  padding: 10px 30px 10px 10px;
  line-height: 17px;
}
.chrome-bootstrap .alert .delete {
  top: 5px;
  right: 6px;
  opacity: 1;
}
.chrome-bootstrap ul.tags li {
  background: #8AAAED;
  color: #FFF;
  border-radius: 3px;
  position: relative;
  display: inline-block;
  padding: 2px 5px;
}
.chrome-bootstrap ul.tags li a {
  color: #FFF;
  text-decoration: none;
}
.chrome-bootstrap ul.tags li a:hover {
  text-decoration: underline;
}
.chrome-bootstrap ul.tags li .delete {
  opacity: 1;
  position: relative;
  display: inline-block;
  width: 13px;
  height: 12px;
  top: 1px;
  background-position-y: -1px;
}
.chrome-bootstrap ul.menu {
  -webkit-margin-before: 1em;
  -webkit-margin-after: 2em;
  -webkit-margin-start: 0px;
  -webkit-margin-end: 0px;
  -webkit-padding-start: 40px;
  list-style-type: none;
  padding: 0;
}
.chrome-bootstrap ul.menu li {
  -webkit-border-start: 6px solid transparent;
  -webkit-padding-start: 18px;
  -webkit-user-select: none;
  display: list-item;
  text-align: -webkit-match-parent;
}
.chrome-bootstrap ul.menu li.selected {
  -webkit-border-start-color: #4e5764;
}
.chrome-bootstrap ul.menu li.selected a {
  color: #464E5A;
}
.chrome-bootstrap ul.menu li a {
  border: 0;
  color: #999;
  cursor: pointer;
  font: inherit;
  line-height: 29px;
  margin: 0;
  padding: 0;
  text-decoration: none;
  display: block;
}
.chrome-bootstrap .arrow_collapse {
  border-top: 5px solid transparent;
  border-bottom: 5px solid transparent;
  border-left: 6px solid #999;
  -webkit-margin-end: 4px;
  top: 1px;
}
.chrome-bootstrap .arrow_expand {
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 7px solid #999;
  -webkit-margin-end: 4px;
}
.chrome-bootstrap .arrow {
  width: 0;
  height: 0;
  position: relative;
  display: inline-block;
}
.chrome-bootstrap .delete {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAQAAAC1+jfqAAAAiElEQVR42r2RsQrDMAxEBRdl8SDcX8lQPGg1GBI6lvz/h7QyRRXV0qUULwfvwZ1tenw5PxToRPWMC52eA9+WDnlh3HFQ/xBQl86NFYJqeGflkiogrOvVlIFhqURFVho3x1moGAa3deMs+LS30CAhBN5nNxeT5hbJ1zwmji2k+aF6NENIPf/hs54f0sZFUVAMigAAAABJRU5ErkJggg==");
  background-repeat: no-repeat;
  display: block;
  opacity: 0;
  height: 14px;
  width: 14px;
  -webkit-transition: 150ms opacity;
  background-color: transparent;
  text-indent: -5000px;
  position: absolute;
}
.chrome-bootstrap .delete:hover {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAQAAAC1+jfqAAAAqklEQVR4XqWRMQ6DMAxF/1Fyilyj2SmIBUG5QcTCyJA5Z8jGhlBPgRi4TmoDraVmKFJlWYrlp/g5QfwRlwEVNWVa4WzfH9jK6kCkEkBjwxOhLghheMWMELUAqqwQ4OCbnE4LJnhr5IYdqQt4DJQjhe9u4vBBmnxHHNzRFkDGjHDo0VuTAqy2vAG4NkvXXDHxbGsIGlj3e835VFNtdugma/Jk0eXq0lP//5svi4PtO01oFfYAAAAASUVORK5CYII=");
}
.chrome-bootstrap .highlightable li {
  position: relative;
  padding: 2px 0;
}
.chrome-bootstrap .highlightable li:hover > a:not(.action),
.chrome-bootstrap .highlightable li a:not(.action):focus {
  background-color: #F0F6FE;
  color: #555;
}
.chrome-bootstrap .highlightable li:hover > .action {
  opacity: 0.7;
}
.chrome-bootstrap .highlightable li a {
  padding: 5px;
  display: block;
  position: relative;
  z-index: 0;
  text-decoration: none;
}
.chrome-bootstrap .highlightable li dt {
  font-size: 105%;
  margin-bottom: 3px;
}
.chrome-bootstrap .highlightable li dd {
  color: #999;
  overflow: hidden;
  white-space: nowrap;
  font-size: 10px;
  margin-top: 5px;
}
.chrome-bootstrap .highlightable li .tags {
  float: left;
  margin-top: -1px;
  font-size: 12px;
}
.chrome-bootstrap .highlightable li .tags li:last-child {
  margin-right: 5px;
}
.chrome-bootstrap .highlightable li .tags li:hover > a:not(.action) {
  background: #8AAAED;
  color: #FFF;
}
.chrome-bootstrap .highlightable li .tags li a {
  padding: 0;
}
.chrome-bootstrap .highlightable li .action {
  -webkit-appearance: none;
  -webkit-transition: opacity 150ms;
  background: #8AAAED;
  border: none;
  border-radius: 2px;
  color: white;
  opacity: 0;
  margin-top: 0;
  font-size: 10px;
  padding: 1px 6px;
  position: absolute;
  top: 8px;
  right: 32px;
  -webkit-transition: 150ms opacity;
  cursor: pointer;
}
.chrome-bootstrap .highlightable li .action:hover {
  opacity: 1;
}
.chrome-bootstrap .highlightable li .highlightable {
  -webkit-margin-start: 30px;
}
.chrome-bootstrap .highlightable.editable .delete {
  position: absolute;
  top: 7px;
  right: 5px;
}
.chrome-bootstrap .highlightable.editable li:hover > .delete {
  opacity: 1;
}
.chrome-bootstrap .highlightable.draggable .handle {
  width: 8px;
  height: 41px;
  background-image: linear-gradient(to bottom, #c1c1c1 50%, rgba(255, 255, 255, 0) 0%);
  background-position: center;
  background-size: 100% 17%;
  background-repeat: repeat-y;
  visibility: hidden;
  position: absolute;
  top: 4px;
  left: 2px;
}
.chrome-bootstrap .highlightable.draggable .handle:hover {
  cursor: move;
  cursor: -webkit-grab;
  display: block;
}
.chrome-bootstrap .highlightable.draggable .handle:after {
  margin-left: 3px;
  width: 2px;
  height: 41px;
  background: #F0F6FE;
  content: "";
  display: block;
}
.chrome-bootstrap .highlightable.draggable li:hover .handle {
  visibility: visible;
  z-index: 1;
}
.chrome-bootstrap .highlightable.draggable li > .item {
  padding-left: 20px;
}
.chrome-bootstrap .match {
  background: #f2f37b;
  display: inline-block;
  margin: 0 1px;
}
.chrome-bootstrap select,
.chrome-bootstrap input[type='checkbox'],
.chrome-bootstrap input[type='radio'],
.chrome-bootstrap input[type='button'],
.chrome-bootstrap button {
  -webkit-appearance: none;
  -webkit-user-select: none;
  background-image: -webkit-linear-gradient(#ededed, #ededed 38%, #dedede);
  border: 1px solid rgba(0, 0, 0, 0.25);
  border-radius: 2px;
  box-shadow: 0 1px 0 rgba(0, 0, 0, 0.08), inset 0 1px 2px rgba(255, 255, 255, 0.75);
  color: #444;
  font: inherit;
  margin: 0 1px 0 0;
  text-shadow: 0 1px 0 #F0F0F0;
}
.chrome-bootstrap button.small {
  padding: 1px 5px 2px;
  min-height: 1em;
}
.chrome-bootstrap input[type='checkbox']:checked::before {
  -webkit-user-select: none;
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAsAAAALCAYAAACprHcmAAAAAXNSR0IArs4c6QAAAAZiS0dEAP8A/wD/oL2nkwAAAAlwSFlzAAALEwAACxMBAJqcGAAAAAd0SU1FB9wDBhYcG79aGIsAAACbSURBVBjTjdFBCkFhFAXgj4fp24PBy0SZ2ICRXRgYGb2xlKzBSEo2YgsiKWVoZgFKMjD5X/2Ux6lb99bpnNO5lKMR5i8MsEQHkhJiEzlS9HCqfiFWMUIt3AfsC3KKLCL30Qr7HfM4Ro4h6rhiEqmusIMKuphGqo+ogSPGcbYLzh91vdkXSHDDBk+0gxussS3rNcMCs+D6E18/9gLPPhbDshfzLgAAAABJRU5ErkJggg==");
  background-size: 100% 100%;
  content: '';
  display: block;
  height: 100%;
  width: 100%;
}
.chrome-bootstrap html[dir='rtl'] input[type='checkbox']:checked::before {
  -webkit-transform: scaleX(-1);
}
.chrome-bootstrap input[type='radio']:checked::before {
  background-color: #666;
  border-radius: 100%;
  bottom: 3px;
  content: '';
  display: block;
  left: 3px;
  position: absolute;
  right: 3px;
  top: 3px;
}
.chrome-bootstrap select {
  -webkit-appearance: none;
  -webkit-padding-end: 20px;
  -webkit-padding-start: 6px;
  /* OVERRIDE */

  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABMAAAAICAYAAAAbQcSUAAAAWklEQVQokWNgoAOIAuI0PDiKaJMSgYCZmfkbkPkfHYPEQfJEG/b//3+FBQsWLGRjY/uJbBCIDxIHyRNtGDYDyTYI3UA+Pr4vFBmEbODbt2+bKDYIyUBWYtQBAIRzRP/XKJ//AAAAAElFTkSuQmCC), -webkit-linear-gradient(#ededed, #ededed 38%, #dedede);
  background-position: right center;
  background-repeat: no-repeat;
}
.chrome-bootstrap select {
  min-height: 2em;
  min-width: 4em;
}
.chrome-bootstrap html[dir='rtl'] select {
  background-position: center left;
}
.chrome-bootstrap input[type='checkbox'] {
  bottom: 2px;
  height: 13px;
  position: relative;
  vertical-align: middle;
  width: 13px;
}
.chrome-bootstrap input[type='radio'] {
  /* OVERRIDE */

  border-radius: 100%;
  bottom: 3px;
  height: 15px;
  position: relative;
  vertical-align: middle;
  width: 15px;
}
.chrome-bootstrap button {
  -webkit-padding-end: 10px;
  -webkit-padding-start: 10px;
  min-height: 2em;
  min-width: 4em;
}
.chrome-bootstrap input[type='text'],
.chrome-bootstrap input[type='number'],
.chrome-bootstrap input[type='password'],
.chrome-bootstrap input[type='search'] {
  border: 1px solid #BFBFBF;
  border-radius: 2px;
  box-sizing: border-box;
  color: #444;
  font: inherit;
  margin: 0;
  min-height: 2em;
  padding: 3px;
  padding-bottom: 4px;
}
.chrome-bootstrap .radio,
.chrome-bootstrap .checkbox {
  margin: 0.65em 0;
}
.chrome-bootstrap .link-button {
  -webkit-box-shadow: none !important;
  box-shadow: none !important;
  background: transparent none !important;
  border: none !important;
  border-color: transparent !important;
  color: #1155cc !important;
  cursor: pointer;
  font: inherit;
  margin: 0;
  padding: 0 4px;
}
.chrome-bootstrap .link-button:hover {
  text-decoration: underline;
}
.chrome-bootstrap .link-button:active {
  color: #052577 !important;
  text-decoration: underline;
}
.chrome-bootstrap select:focus,
.chrome-bootstrap input[type='checkbox']:focus,
.chrome-bootstrap input[type='password']:focus,
.chrome-bootstrap input[type='radio']:focus,
.chrome-bootstrap input[type='search']:focus,
.chrome-bootstrap input[type='text']:focus,
.chrome-bootstrap input[type='number']:focus,
.chrome-bootstrap button:focus {
  /* OVERRIDE */

  -webkit-transition: border-color 200ms;
  /* We use border color because it follows the border radius (unlike outline).
       * This is particularly noticeable on mac. */

  border-color: #4d90fe;
  outline: none;
}
.chrome-bootstrap button:disabled,
.chrome-bootstrap select:disabled {
  background-image: -webkit-linear-gradient(#f1f1f1, #f1f1f1 38%, #e6e6e6);
  border-color: rgba(80, 80, 80, 0.2);
  box-shadow: 0 1px 0 rgba(80, 80, 80, 0.08), inset 0 1px 2px rgba(255, 255, 255, 0.75);
  color: #aaa;
  cursor: default;
}
.chrome-bootstrap select:disabled {
  /* OVERRIDE */

  background-image: -webkit-image-set(url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABMAAAAICAYAAAAbQcSUAAAAAXNSR0IArs4c6QAAAAd0SU1FB9sLAxYEBKriBmwAAAAGYktHRAD/AP8A/6C9p5MAAAAJcEhZcwAACxMAAAsTAQCanBgAAABLSURBVCiRY2CgA4gC4jQ8OIpokxKBoKGh4T8uDJIn2rD///8rLFiwYCE2g0DiIHkSfIndQLIMwmYgRQYhG/j27dsmig1CMpCVGHUAo8FcsHfxfXQAAAAASUVORK5CYII=") 1 x), -webkit-linear-gradient(#f1f1f1, #f1f1f1 38%, #e6e6e6);
}
.chrome-bootstrap input[type='checkbox']:disabled,
.chrome-bootstrap input[type='radio']:disabled {
  opacity: .75;
}
.chrome-bootstrap input[type='search']:disabled,
.chrome-bootstrap input[type='number']:disabled,
.chrome-bootstrap input[type='password']:disabled,
.chrome-bootstrap input[type='text']:disabled {
  color: #999;
}
.chrome-bootstrap select:hover:enabled,
.chrome-bootstrap input[type='checkbox']:hover:enabled,
.chrome-bootstrap input[type='radio']:hover:enabled,
.chrome-bootstrap button:hover:enabled {
  background-image: -webkit-linear-gradient(#f0f0f0, #f0f0f0 38%, #e0e0e0);
  border-color: rgba(0, 0, 0, 0.3);
  box-shadow: 0 1px 0 rgba(0, 0, 0, 0.12), inset 0 1px 2px rgba(255, 255, 255, 0.95);
  color: black;
}
.chrome-bootstrap select:hover:enabled {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABMAAAAICAYAAAAbQcSUAAAAWklEQVQokWNgoAOIAuI0PDiKaJMSgYCZmfkbkPkfHYPEQfJEG/b//3+FBQsWLGRjY/uJbBCIDxIHyRNtGDYDyTYI3UA+Pr4vFBmEbODbt2+bKDYIyUBWYtQBAIRzRP/XKJ//AAAAAElFTkSuQmCC"), -webkit-linear-gradient(#f0f0f0, #f0f0f0 38%, #e0e0e0);
}
.chrome-bootstrap select:active:enabled,
.chrome-bootstrap input[type='checkbox']:active:enabled,
.chrome-bootstrap input[type='radio']:active:enabled,
.chrome-bootstrap button:active:enabled {
  background-image: -webkit-linear-gradient(#e7e7e7, #e7e7e7 38%, #d7d7d7);
  box-shadow: none;
  text-shadow: none;
}
.chrome-bootstrap select:active:enabled {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABMAAAAICAYAAAAbQcSUAAAAWklEQVQokWNgoAOIAuI0PDiKaJMSgYCZmfkbkPkfHYPEQfJEG/b//3+FBQsWLGRjY/uJbBCIDxIHyRNtGDYDyTYI3UA+Pr4vFBmEbODbt2+bKDYIyUBWYtQBAIRzRP/XKJ//AAAAAElFTkSuQmCC"), -webkit-linear-gradient(#e7e7e7, #e7e7e7 38%, #d7d7d7);
}
.chrome-bootstrap .overlay {
  -webkit-box-align: center;
  -webkit-box-orient: vertical;
  -webkit-box-pack: center;
  -webkit-transition: opacity .2s;
  background-color: rgba(255, 255, 255, 0.75);
  bottom: 0;
  display: -webkit-box;
  left: 0;
  overflow: auto;
  padding: 20px;
  position: fixed;
  right: 0;
  top: 0;
  z-index: 5;
  opacity: 1;
}
.chrome-bootstrap .overlay.transparent {
  opacity: 0;
}
.chrome-bootstrap .overlay.transparent .page {
  -webkit-transform: scale(0.99) translateY(-20px);
}
.chrome-bootstrap .overlay .page {
  -webkit-border-radius: 3px;
  -webkit-box-orient: vertical;
  -webkit-transition: 200ms -webkit-transform;
  background: white;
  box-shadow: 0 4px 23px 5px rgba(0, 0, 0, 0.2), 0 2px 6px rgba(0, 0, 0, 0.15);
  color: #333;
  display: -webkit-box;
  min-width: 400px;
  padding: 0;
  position: relative;
  overflow: hidden;
}
@-webkit-keyframes pulse {
  0% {
    -webkit-transform: scale(1);
  }
  40% {
    -webkit-transform: scale(1.02);
  }
  60% {
    -webkit-transform: scale(1.02);
  }
  100% {
    -webkit-transform: scale(1);
  }
}
.chrome-bootstrap .overlay .page.pulse {
  -webkit-animation-duration: 180ms;
  -webkit-animation-iteration-count: 1;
  -webkit-animation-name: pulse;
  -webkit-animation-timing-function: ease-in-out;
}
.chrome-bootstrap .overlay .page h1 {
  -webkit-padding-end: 24px;
  -webkit-user-select: none;
  color: #333;
  font-size: 120%;
  margin: 0;
  padding: 14px 17px 14px;
  text-shadow: white 0 1px 2px;
}
.chrome-bootstrap .overlay .page ul li {
  padding: 5px 0;
}
.chrome-bootstrap .overlay .page ul.tags li {
  padding: 2px 5px;
}
.chrome-bootstrap .overlay .page .content-area {
  -webkit-box-flex: 1;
  overflow: auto;
  padding: 6px 17px 6px;
}
.chrome-bootstrap .overlay .page .close-button {
  background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAQAAAC1QeVaAAAAUklEQVR4XqXPYQrAIAhAYW/gXd8NJxTopVqsGEhtf+L9/ERU2k/HSMFQpKcYJeNFI9Be0LCMij8cYyjj5EHIivGBkwLfrbX3IF8PqumVmnDpEG+eDsKibPG2JwAAAABJRU5ErkJggg==');
  background-position: center;
  background-repeat: no-repeat;
  height: 14px;
  position: absolute;
  right: 7px;
  top: 7px;
  width: 14px;
}
.chrome-bootstrap .overlay .page .close-button:hover {
  background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAQAAAC1QeVaAAAAnUlEQVR4XoWQQQ6CQAxFewjkJkMCyXgJPMk7AiYczyBeZEAX6AKctGIaN+bt+trk9wtGQc/IkhnoKGxqqiWxOSZalapWFZ6VrIUDExsN0a5JRBq9LoVOR0eEQMoEhKizXhhsn0p1sCWVo7CwOf1RytPL8CPvwuBUoHL6ugeK30CVD1TqK7V/hdpe+VNChhOzV8xWny/+xosHF8578W/Hmc1OOC3wmwAAAABJRU5ErkJggg==');
}
.chrome-bootstrap .overlay .page .action-area {
  -webkit-box-align: center;
  -webkit-box-orient: horizontal;
  -webkit-box-pack: end;
  display: -webkit-box;
  padding: 14px 17px;
}
.chrome-bootstrap .overlay .page .action-area-right {
  display: -webkit-box;
}
.chrome-bootstrap .overlay .page .button-strip {
  -webkit-box-orient: horizontal;
  display: -webkit-box;
}
.chrome-bootstrap .overlay .page .button-strip button {
  -webkit-margin-start: 10px;
  display: block;
}
