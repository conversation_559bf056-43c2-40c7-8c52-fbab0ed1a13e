ChangeLog
===================================================

2022-1-5   (1.4.1)
    1. Updated Russian translation

2021-12-09 (1.3.9)
    1. fix popup.html

2021-12-07 (1.3.8)
    1. fix whitelist

2021-11-30 (1.3.7)
    1. UI improvement

2021-11-27 (1.3.6)
    1. add pac url support

2021-07-27
    1. use pac data instead of pac url
    2. update jquery to 3.6.0
    3. update Russian translation

2020-12-1 (1.3.3)
    1. bug fixed
    2. improve UI

2020-11-23 (1.3.0)
    1. support quic proxy
    2. delete file:// scheme in pac mode (chrome delete file:// support in extension)
    3. bugs fixed

2017-12-14 (1.2.7)
    1. Change font size bigger
    2. Fix some html error
    3. code refactor

2014-8-12 (1.2.6)
    1. Rewrite UI

2014-06-30 (1.2.5)
    1. Add Alt-o keyboard shortcut to go to option page

2014-03-10 (1.2.4)
    1. Support extension settings synchronize
    2. Update UI

2014-02-26 (1.2.3)
    1. Update internal china bypass list automatically
    2. Update UI (better UI for Mac OSX users)

2013-12-18 (1.2.2)
    1. Add proxy auto_detect mode support (WPAD)
    2. Import old proxy settings automatically
    3. Bug fixed
    4. Update UI

2013-7-25 (1.2)
    1. Add proxy authentication support
    2. Reload proxy settings automaticlly after save
    3. Add default china bypass list
    4. Completely rewritten UI
    5. Add Chinese language support

2012-12-7 (1.1.4)
    1. Support direct mode, connect never use a proxy.
    2. Update UI and bugs fixed.
