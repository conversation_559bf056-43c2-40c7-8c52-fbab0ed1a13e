{"manifest_version": 3, "name": "HTTP Proxy Auto Config", "version": "1.0.0", "description": "自动设置HTTP密码代理的Chrome扩展", "permissions": ["proxy", "storage", "webRequest", "webRequestAuthProvider"], "host_permissions": ["<all_urls>"], "background": {"service_worker": "background.js"}, "web_accessible_resources": [{"resources": ["proxy-config.json", "debug.html"], "matches": ["<all_urls>"]}], "action": {"default_popup": "debug.html", "default_title": "HTTP代理调试"}}