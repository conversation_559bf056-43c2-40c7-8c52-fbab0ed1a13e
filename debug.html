<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>代理调试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .button { padding: 10px 20px; margin: 10px; background: #4CAF50; color: white; border: none; cursor: pointer; }
        .button:hover { background: #45a049; }
        .result { margin: 10px 0; padding: 10px; background: #f0f0f0; border-radius: 5px; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
    </style>
</head>
<body>
    <h1>HTTP代理调试工具</h1>
    
    <div>
        <button class="button" onclick="checkProxyStatus()">检查代理状态</button>
        <button class="button" onclick="testConnection()">测试连接</button>
        <button class="button" onclick="checkIP()">检查当前IP</button>
        <button class="button" onclick="reloadConfig()">重新加载配置</button>
    </div>
    
    <div id="results"></div>

    <script>
        function addResult(message, isError = false) {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = 'result ' + (isError ? 'error' : 'success');
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            results.insertBefore(div, results.firstChild);
        }

        async function checkProxyStatus() {
            try {
                const result = await chrome.proxy.settings.get({});
                addResult(`代理状态: ${JSON.stringify(result.value, null, 2)}`);
            } catch (error) {
                addResult(`检查代理状态失败: ${error.message}`, true);
            }
        }

        async function testConnection() {
            try {
                addResult('正在测试连接...');
                const response = await fetch('https://httpbin.org/ip');
                const data = await response.json();
                addResult(`当前IP: ${data.origin}`);
            } catch (error) {
                addResult(`连接测试失败: ${error.message}`, true);
            }
        }

        async function checkIP() {
            try {
                addResult('正在检查IP地址...');
                const response = await fetch('https://api.ipify.org?format=json');
                const data = await response.json();
                addResult(`外部IP: ${data.ip}`);
            } catch (error) {
                addResult(`IP检查失败: ${error.message}`, true);
            }
        }

        async function reloadConfig() {
            try {
                addResult('正在重新加载配置...');
                // 发送消息给background script重新加载配置
                chrome.runtime.sendMessage({action: 'reloadConfig'}, (response) => {
                    if (response && response.success) {
                        addResult('配置重新加载成功');
                    } else {
                        addResult('配置重新加载失败', true);
                    }
                });
            } catch (error) {
                addResult(`重新加载配置失败: ${error.message}`, true);
            }
        }

        // 页面加载时自动检查状态
        window.onload = function() {
            addResult('调试页面已加载');
            checkProxyStatus();
        };
    </script>
</body>
</html>
